Feature: File Screen

  Background:
    Given The user is on File Screen

  @e2e @file-screen @skip
  Scenario: Verify user can see file detail when clicking on a file name
    When The user selects the file named "4s testing tdo.mp4"
    Then The following file details should be visible:
      | Field              | Expected Value                 |
      | File Name          |             4s testing tdo.mp4 |
      | Upload Date        | Uploaded: 5/29/2025, 11:34 AM  |
      | File Creator       | File Uploaded by <PERSON><PERSON>   |
      | File Size          | File Size: 1.5 Mb              |
      | GPS Location       | File GPS Location: Unavailable |
      | View File Button   | View File                      |
      | Delete File Button | Delete File                    |
  # @e2e @file-screen
  # Scenario: Verify user can edit file name
  #   When The user selects the file named "4s testing tdo.mp4"
  #   And The user clicks the file name edit button
  #   And The user changes the name to "renamed file.mp4"
  #   Then The name of the file should be updated to "renamed file.mp4"
  # @e2e @file-screen
  # Scenario: Verify user can view file details
  #   When The user selects the file named "4s testing tdo.mp4"
  #   And The user clicks on "View File"
  #   Then The user should navigate to file details page
  # @e2e @file-screen @skip
  # Scenario: Verify user can delete a file successfully
  #   Given The user deletes the following Files if exist: "e2e-delete-test-file.mp4"
  #   When The user uploads test file "e2e-delete-test-file.mp4"
  #   Then The user should see a success snackbar with message "e2e-delete-test-file.mp4"
  #   When The user selects the file named "e2e-delete-test-file.mp4"
  #   And The user clicks on "Delete File"
  #   And The user enters correct file name "e2e-delete-test-file.mp4"
  #   Then The user verifies delete button is enabled and clicks it
  #   Then The user should see a success snackbar with message "Successfully deleted file e2e-delete-test-file.mp4"
  # @e2e @file-screen
  # Scenario: Verify user can not delete a file with wrong file name confirmation
  #   When The user selects the file named "4s testing tdo.mp4"
  #   And The user clicks on "Delete File"
  #   And The user enters wrong file name "Wrong Name"
  #   Then The "Delete" button should still be disabled and textbox highlighted in red
  # @e2e @file-screen
  # Scenario: Verify results per page functionality
  #   Then The user should see the "Results Per Page" label
  #   When The user changes the results per page and verifies the following options:
  #     | PerPage |
  #     | 100     |
  #     | 10      |
  #     | 50      |
  # @e2e @file-screen
  # Scenario: Verify user can move between pages
  #   Then The user should see the initial pagination state
  #   When The user navigates to the "next" page
  #   Then The pagination should update for the next page
  #   When The user navigates to the "previous" page
  #   Then The pagination should return to the initial state
  # @e2e @file-screen @skip
  # Scenario: Delete Files after run
  #   Given The user deletes the following Files if exist:
  #     | fileName                   |
  #     | e2e-delete-test-file.mp4   |
  #     | renamed file.mp4           |
