import { When, Then, DataTable, Given, Before } from "@badeball/cypress-cucumber-preprocessor";
import { mainPage } from "../../../pages/mainPage";
import { ButtonActionMap, ButtonActions, ValidationButtonTypes } from "../../../support/helperFunction/eventScreenHelper";

Before( { tags: '@event-screen' }, () => {
  cy.LoginLandingPage();
  cy.intercept('GET', /api\/v1\/events\/\?sortBy/).as('fetchEvent');
});

Given('The user is on Event Screen', () => {
  mainPage.visit();
})

When('The user clicks on the {string} tab', (tabName: string) => {
  mainPage.clickTab(tabName);
  if (tabName.toLowerCase() === 'files') {
    cy.waitFilesTabIsLoaded();
  } else {
    cy.waitMainPageIsLoaded();
  }
});

Then('The {string} tab should be active', (activeTabName: string) => {
  mainPage.verifyTabIsActive(activeTabName);
});

When('The user clicks the {string} breadcrumb', (breadcrumbText: string) => {
  mainPage.clickBreadcrumb(breadcrumbText);
});

Then('The page should not navigate away', () => {
  mainPage.verifyPageNotNavigated();
});

When('The user selects the event named {string}', (eventName: string) => {
  mainPage.selectEvent(eventName);
});

Then('The following event details should be visible:', (dataTable: DataTable) => {
  const rows = dataTable.hashes() as unknown as Array<{ Field: string; 'Expected Value': string }>;
  mainPage.verifyEventDetails(rows);
});

Then('Event Time has format: {string}', (format: string) => {
  mainPage.verifyEventTimeFormat(format);
});

When('The user clicks the event name located on the right side', () => {
  mainPage.clickEventNameEdit();
});

When('The user changes the name to {string}', (newName: string) => {
  mainPage.changeEventName(newName);
});

Then('The name of the event should be updated to {string}', (newName: string) => {
  mainPage.verifyEventNameChanged(newName);
});

When('The user clicks on {string}', (buttonText: string) => {
  const buttonActionMap: ButtonActionMap = {
    [ButtonActions.VIEW_EVENT]: () => mainPage.clickViewEventButton(),
    [ButtonActions.DELETE_EVENT]: () => mainPage.clickDeleteEventButton(),
  };
  const action = buttonActionMap[buttonText as ButtonActions];
  action();
});

Then('The user should navigate to event details page', () => {
  mainPage.verifyNavigationToEventDetails();
});

When('The user enters wrong event name {string}', (wrongName: string) => {
  mainPage.enterWrongEventName(wrongName);
});

Then('The {string} button should still be disabled and textbox highlighted in red', (buttonText: string) => {
  if (buttonText === ValidationButtonTypes.DELETE) {
    mainPage.verifyDeleteButtonDisabledAndTextboxError();
  }
});

When('The user clicks on the {string} column header', (columnName: string) => {
  mainPage.clickColumnHeader(columnName);
});

Then('{string} is sorted by {string}', (columnName: string, sortedBy: string) => {
  mainPage.clickColumnHeaderUntilSorted(columnName, sortedBy);
  mainPage.verifyColumnSortState(columnName, sortedBy);
});

When('The user enters {string} into the search bar', (keyword: string) => {
  mainPage.enterSearchKeyword(keyword);
});

Then('The displayed {word} results should contain {string}', (searchType: string, keyword: string) => {
  if (searchType === 'event' || searchType === 'file') {
    const pluralSearchType = `${searchType}s` as 'events' | 'files';
    mainPage.verifySearchResults(keyword, pluralSearchType);
  } else {
    throw new Error(`Unsupported search type: ${searchType}. Expected 'event' or 'file'.`);
  }
});

Then('The user should see the {string} label', (label: string) => {
  if (label === 'Results Per Page') {
    mainPage.verifyResultsPerPageLabel();
  }
});

When('The user changes the results per page and verifies the following options:', (dataTable: DataTable) => {
  const rows = dataTable.hashes();
  rows.forEach((row) => {
    const perPage = row.PerPage;
    mainPage.changeResultsPerPage(perPage);
    mainPage.verifyResultsPerPageChanged(perPage);
  });
});

Then('The user should see the initial pagination state', () => {
  mainPage.verifyPaginationInitialState();
});

When('The user navigates to the {string} page', (direction: 'next' | 'previous') => {
  if (direction === 'next') {
    mainPage.clickNextPage();
  } else {
    mainPage.clickPreviousPage();
  }
});

Then('The pagination should update for the next page', () => {
  mainPage.verifyNavigatedToNextPage();
});

Then('The pagination should return to the initial state', () => {
  mainPage.verifyNavigatedToPreviousPage();
});

Given('A test event named {string} exists for deletion testing', (eventName: string) => {
  mainPage.createTestEvent(eventName);
  cy.waitMainPageIsLoaded();
});

When('The user navigates to the event deletion functionality for {string}', (eventName: string) => {
  mainPage.selectEvent(eventName);
});

When('The user clicks on the Delete event button', () => {
  mainPage.clickDeleteEventButton();
});

Then('A confirmation dialog should appear with the message for {string}', (eventName: string) => {
  mainPage.verifyDeleteConfirmationDialog(eventName);
});

When('The user enters the event name {string} in the confirmation textbox', (eventName: string) => {
  mainPage.enterEventNameForDeletion(eventName);
});

When('The user clicks on the Delete button in the confirmation dialog', () => {
  mainPage.confirmEventDeletion();
});

Then('The event {string} should be successfully removed from the system', (eventName: string) => {
  mainPage.verifyEventDeleted(eventName);
});

When('The user clicks upload files button', () => {
  mainPage.clickUploadFilesButton();
});

When('The user clicks on new event button', () => {
  mainPage.clickNewEvent();
});

Then('The user enter {string} in the event name textbox', (eventName: string) => {
  mainPage.enterEventName(eventName);
});

Then('The user clicks on create event button', () => {
  mainPage.clickCreateEvent();
});

Then('The user should see a success snackbar with message {string}', (message: string) => {
  mainPage.verifySuccessSnackbar(message);
});

Then('The user clicks on cancel upload button', () => {
  mainPage.clickCancelUpload();
});

When('The user creates default event name {string}', (eventName: string) => {
  mainPage.clickUploadFilesButton();
  mainPage.clickNewEvent();
  mainPage.enterEventName(eventName);
  mainPage.clickCreateEvent();
})

Then('The user should see {string} in the event table', (eventName: string) => {
  cy.awaitNetworkResponseCode({ alias: '@fetchEvent', code: 200 });
  mainPage.verifyEventInTable(eventName);
});

Then('The user verifies delete button is enabled and clicks it', () => {
  mainPage.verifyDeleteButtonEnabledAndClick();
});

When('The user selects a file to upload {string}', (fileName: string) => {
  cy.SelectFile({ fileName: fileName });
});

Then('The file should be uploaded successfully', () => {
  mainPage.verifyFileUploadedSuccessfully();
});

Then('The file {string} should appear in the upload area', (fileName: string) => {
  mainPage.verifyFileAppearsInUploadArea(fileName);
});

Then('The user clicks upload button', () => {
  mainPage.clickUploadButton();
});

Then('The upload should complete {string}', (progress: string) => {
  mainPage.verifyUploadComplete(progress);
});

Given(
  /^The user deletes the following Events if exist:(?:\s*"([^"]+)")?$/,
  function (
    eventName: string | undefined,
    eventNamesDataTable: DataTable | undefined
  ) {
    mainPage.deleteEventsIfExist(
      eventName,
      eventNamesDataTable
    );
  }
);
